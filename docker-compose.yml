version: '3.8'

services:
  cursor-mover:
    build: .
    container_name: cursor-mover
    environment:
      - DISPLAY=${DISPLAY}
    volumes:
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
    network_mode: host
    stdin_open: true
    tty: true
    command: ["--pattern", "circle", "--speed", "0.01"]
    
    # Alternative configurations for different patterns:
    # Uncomment one of these to use different patterns:
    
    # Square pattern:
    # command: ["--pattern", "square", "--speed", "0.02"]
    
    # Figure-8 pattern:
    # command: ["--pattern", "figure8", "--speed", "0.005"]
    
    # Spiral pattern:
    # command: ["--pattern", "spiral", "--speed", "0.01"]
    
    # Random movement:
    # command: ["--pattern", "random", "--speed", "0.1"]
