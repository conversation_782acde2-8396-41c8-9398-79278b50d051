# Use Python 3.11 slim image as base
FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV DISPLAY=:0

# Install system dependencies required for GUI applications and pyautogui
RUN apt-get update && apt-get install -y \
    python3-tk \
    python3-dev \
    scrot \
    xvfb \
    x11-apps \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Create requirements file
RUN echo "pyautogui==0.9.54" > requirements.txt

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the cursor mover script
COPY cursor_mover.py .

# Make the script executable
RUN chmod +x cursor_mover.py

# Create a non-root user for security
RUN useradd -m -u 1000 cursoruser && chown -R cursoruser:cursoruser /app
USER cursoruser

# Set the default command
ENTRYPOINT ["python3", "cursor_mover.py"]
CMD ["--pattern", "circle", "--speed", "0.01"]
