#!/usr/bin/env python3
"""
Cursor Mover - A Python script that moves the mouse cursor in various patterns
"""

import time
import math
import argparse
import sys
import signal
from typing import Tuple, List

try:
    import pyautogui
except ImportError:
    print("Error: pyautogui is required. Install it with: pip install pyautogui")
    sys.exit(1)

# Disable pyautogui's fail-safe feature for smoother operation
pyautogui.FAILSAFE = False

class CursorMover:
    def __init__(self, speed: float = 0.01, pattern: str = "circle"):
        """
        Initialize the cursor mover
        
        Args:
            speed: Delay between cursor movements (seconds)
            pattern: Movement pattern ('circle', 'square', 'figure8', 'random', 'spiral')
        """
        self.speed = speed
        self.pattern = pattern
        self.running = True
        self.screen_width, self.screen_height = pyautogui.size()
        self.center_x = self.screen_width // 2
        self.center_y = self.screen_height // 2
        
        # Set up signal handler for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        print(f"\nReceived signal {signum}. Stopping cursor mover...")
        self.running = False
    
    def move_in_circle(self, radius: int = 100, steps: int = 360):
        """Move cursor in a circular pattern"""
        print(f"Moving cursor in circle pattern (radius: {radius})")
        step = 0
        while self.running:
            angle = (step * 2 * math.pi) / steps
            x = self.center_x + int(radius * math.cos(angle))
            y = self.center_y + int(radius * math.sin(angle))
            
            # Ensure coordinates are within screen bounds
            x = max(0, min(x, self.screen_width - 1))
            y = max(0, min(y, self.screen_height - 1))
            
            pyautogui.moveTo(x, y, duration=0)
            time.sleep(self.speed)
            step = (step + 1) % steps
    
    def move_in_square(self, size: int = 200):
        """Move cursor in a square pattern"""
        print(f"Moving cursor in square pattern (size: {size})")
        half_size = size // 2
        corners = [
            (self.center_x - half_size, self.center_y - half_size),  # Top-left
            (self.center_x + half_size, self.center_y - half_size),  # Top-right
            (self.center_x + half_size, self.center_y + half_size),  # Bottom-right
            (self.center_x - half_size, self.center_y + half_size),  # Bottom-left
        ]
        
        corner_index = 0
        while self.running:
            target_x, target_y = corners[corner_index]
            
            # Ensure coordinates are within screen bounds
            target_x = max(0, min(target_x, self.screen_width - 1))
            target_y = max(0, min(target_y, self.screen_height - 1))
            
            pyautogui.moveTo(target_x, target_y, duration=1)
            time.sleep(self.speed * 100)  # Pause at each corner
            corner_index = (corner_index + 1) % 4
    
    def move_in_figure8(self, radius: int = 100, steps: int = 720):
        """Move cursor in a figure-8 pattern"""
        print(f"Moving cursor in figure-8 pattern (radius: {radius})")
        step = 0
        while self.running:
            t = (step * 2 * math.pi) / steps
            x = self.center_x + int(radius * math.sin(t))
            y = self.center_y + int(radius * math.sin(t) * math.cos(t))
            
            # Ensure coordinates are within screen bounds
            x = max(0, min(x, self.screen_width - 1))
            y = max(0, min(y, self.screen_height - 1))
            
            pyautogui.moveTo(x, y, duration=0)
            time.sleep(self.speed)
            step = (step + 1) % steps
    
    def move_in_spiral(self, max_radius: int = 200, steps: int = 1000):
        """Move cursor in a spiral pattern"""
        print(f"Moving cursor in spiral pattern (max radius: {max_radius})")
        step = 0
        while self.running:
            t = (step * 4 * math.pi) / steps
            radius = (step / steps) * max_radius
            x = self.center_x + int(radius * math.cos(t))
            y = self.center_y + int(radius * math.sin(t))
            
            # Ensure coordinates are within screen bounds
            x = max(0, min(x, self.screen_width - 1))
            y = max(0, min(y, self.screen_height - 1))
            
            pyautogui.moveTo(x, y, duration=0)
            time.sleep(self.speed)
            step = (step + 1) % steps
    
    def move_randomly(self):
        """Move cursor to random positions"""
        print("Moving cursor randomly")
        import random
        while self.running:
            x = random.randint(0, self.screen_width - 1)
            y = random.randint(0, self.screen_height - 1)
            pyautogui.moveTo(x, y, duration=0.5)
            time.sleep(self.speed * 50)  # Longer pause for random movement
    
    def start(self):
        """Start the cursor movement based on the selected pattern"""
        print(f"Screen size: {self.screen_width}x{self.screen_height}")
        print(f"Center: ({self.center_x}, {self.center_y})")
        print(f"Speed: {self.speed}s between movements")
        print("Press Ctrl+C to stop")
        
        try:
            if self.pattern == "circle":
                self.move_in_circle()
            elif self.pattern == "square":
                self.move_in_square()
            elif self.pattern == "figure8":
                self.move_in_figure8()
            elif self.pattern == "spiral":
                self.move_in_spiral()
            elif self.pattern == "random":
                self.move_randomly()
            else:
                print(f"Unknown pattern: {self.pattern}")
                sys.exit(1)
        except Exception as e:
            print(f"Error during cursor movement: {e}")
        finally:
            print("Cursor mover stopped.")

def main():
    parser = argparse.ArgumentParser(description="Move the mouse cursor in various patterns")
    parser.add_argument(
        "--pattern", 
        choices=["circle", "square", "figure8", "spiral", "random"],
        default="circle",
        help="Movement pattern (default: circle)"
    )
    parser.add_argument(
        "--speed",
        type=float,
        default=0.01,
        help="Speed of movement in seconds between steps (default: 0.01)"
    )
    
    args = parser.parse_args()
    
    cursor_mover = CursorMover(speed=args.speed, pattern=args.pattern)
    cursor_mover.start()

if __name__ == "__main__":
    main()
