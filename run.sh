#!/bin/bash

# Cursor Mover Docker Runner Script
# This script simplifies running the cursor mover with Docker

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default values
PATTERN="circle"
SPEED="0.01"
BUILD_IMAGE=false

# Function to display usage
usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -p, --pattern PATTERN    Movement pattern (circle, square, figure8, spiral, random)"
    echo "  -s, --speed SPEED        Movement speed in seconds (default: 0.01)"
    echo "  -b, --build             Build the Docker image before running"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Run with default settings"
    echo "  $0 -p spiral -s 0.005               # Run spiral pattern with fast speed"
    echo "  $0 --pattern random --speed 0.1     # Run random pattern with slow speed"
    echo "  $0 --build                          # Build image and run with defaults"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--pattern)
            PATTERN="$2"
            shift 2
            ;;
        -s|--speed)
            SPEED="$2"
            shift 2
            ;;
        -b|--build)
            BUILD_IMAGE=true
            shift
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            usage
            exit 1
            ;;
    esac
done

# Validate pattern
case $PATTERN in
    circle|square|figure8|spiral|random)
        ;;
    *)
        echo -e "${RED}Invalid pattern: $PATTERN${NC}"
        echo "Valid patterns: circle, square, figure8, spiral, random"
        exit 1
        ;;
esac

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Docker is not installed or not in PATH${NC}"
    exit 1
fi

# Enable X11 forwarding (Linux/macOS)
if [[ "$OSTYPE" == "linux-gnu"* ]] || [[ "$OSTYPE" == "darwin"* ]]; then
    echo -e "${YELLOW}Enabling X11 forwarding...${NC}"
    xhost +local:docker 2>/dev/null || echo -e "${YELLOW}Warning: Could not enable X11 forwarding${NC}"
fi

# Build image if requested
if [ "$BUILD_IMAGE" = true ]; then
    echo -e "${YELLOW}Building Docker image...${NC}"
    docker build -t cursor-mover .
fi

# Check if image exists
if ! docker image inspect cursor-mover &> /dev/null; then
    echo -e "${YELLOW}Image 'cursor-mover' not found. Building...${NC}"
    docker build -t cursor-mover .
fi

# Run the container
echo -e "${GREEN}Starting cursor mover with pattern: $PATTERN, speed: $SPEED${NC}"
echo -e "${YELLOW}Press Ctrl+C to stop${NC}"

docker run --rm -it \
    -e DISPLAY="${DISPLAY:-:0}" \
    -v /tmp/.X11-unix:/tmp/.X11-unix:rw \
    --network host \
    cursor-mover \
    --pattern "$PATTERN" \
    --speed "$SPEED"
