# Cursor Mover

A Python application that moves your mouse cursor in various patterns, containerized with <PERSON><PERSON> for easy deployment and execution.

## Features

- **Multiple Movement Patterns:**
  - Circle: Moves cursor in a circular pattern
  - Square: Moves cursor in a square pattern
  - Figure-8: Moves cursor in a figure-8 pattern
  - Spiral: Moves cursor in an expanding spiral
  - Random: Moves cursor to random positions

- **Configurable Speed:** Adjust the speed of cursor movement
- **Docker Support:** Run in a containerized environment
- **Graceful Shutdown:** Stop with Ctrl+C

## Prerequisites

### For Docker (Recommended)
- Docker installed on your system
- X11 forwarding enabled (for Linux/macOS)

### For Direct Python Execution
- Python 3.7+
- pyautogui library (`pip install pyautogui`)

## Usage

### Using Docker (Recommended)

#### Build the Docker image:
```bash
docker build -t cursor-mover .
```

#### Run with default settings (circle pattern):
```bash
# On Linux/macOS with X11
xhost +local:docker
docker run --rm -it \
  -e DISPLAY=$DISPLAY \
  -v /tmp/.X11-unix:/tmp/.X11-unix:rw \
  --network host \
  cursor-mover
```

#### Run with custom pattern and speed:
```bash
docker run --rm -it \
  -e DISPLAY=$DISPLAY \
  -v /tmp/.X11-unix:/tmp/.X11-unix:rw \
  --network host \
  cursor-mover --pattern spiral --speed 0.005
```

#### Using Docker Compose:
```bash
# Run with default settings
docker-compose up

# Run in detached mode
docker-compose up -d

# Stop the container
docker-compose down
```

### Direct Python Execution

#### Install dependencies:
```bash
pip install pyautogui
```

#### Run the script:
```bash
# Default circle pattern
python3 cursor_mover.py

# Custom pattern and speed
python3 cursor_mover.py --pattern figure8 --speed 0.02

# See all options
python3 cursor_mover.py --help
```

## Command Line Options

- `--pattern`: Choose movement pattern
  - `circle` (default): Circular movement
  - `square`: Square movement
  - `figure8`: Figure-8 movement
  - `spiral`: Spiral movement
  - `random`: Random movement

- `--speed`: Set movement speed in seconds between steps (default: 0.01)
  - Lower values = faster movement
  - Higher values = slower movement

## Examples

```bash
# Fast circular movement
python3 cursor_mover.py --pattern circle --speed 0.005

# Slow square movement
python3 cursor_mover.py --pattern square --speed 0.05

# Random movement with medium speed
python3 cursor_mover.py --pattern random --speed 0.1
```

## Docker Commands Reference

```bash
# Build the image
docker build -t cursor-mover .

# Run interactively
docker run --rm -it cursor-mover

# Run with X11 forwarding (Linux/macOS)
docker run --rm -it \
  -e DISPLAY=$DISPLAY \
  -v /tmp/.X11-unix:/tmp/.X11-unix:rw \
  --network host \
  cursor-mover

# Run in background
docker run -d --name cursor-mover-bg cursor-mover

# Stop background container
docker stop cursor-mover-bg

# View logs
docker logs cursor-mover-bg
```

## Troubleshooting

### X11 Issues (Linux/macOS)
If you get X11 connection errors:
```bash
# Allow Docker to connect to X11
xhost +local:docker

# Or for more security, allow specific user
xhost +SI:localuser:$(whoami)
```

### Permission Issues
If you encounter permission issues, try running with `--privileged` flag:
```bash
docker run --rm -it --privileged cursor-mover
```

### Windows Support
For Windows users, you may need to:
1. Install an X11 server like VcXsrv or Xming
2. Set the DISPLAY environment variable appropriately
3. Configure the X11 server to allow connections

## Safety Notes

- The application disables pyautogui's fail-safe feature for smoother operation
- Use Ctrl+C to stop the cursor movement gracefully
- Be careful when running in environments where cursor movement might interfere with other applications
- The cursor will move within the bounds of your screen resolution

## License

This project is open source and available under the MIT License.
